import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Brain, Volume2, Target, Lightbulb, Star } from 'lucide-react';
import { ModeComponent } from '../types';
import { getPlaceholderText } from '../utils/answerValidation';
import { getAdventureTheme, getAccentColorClasses } from '../utils/adventureThemes';

interface RecallModeProps extends ModeComponent {
  userAnswer: string;
  onAnswerChange: (answer: string) => void;
  onSubmit: () => void;
  streak: number;
}

export const RecallMode: React.FC<RecallModeProps> = ({
  gameState,
  userAnswer,
  onAnswerChange,
  onSubmit,
  streak,
  isAdventureMode,
  playPronunciation
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Focus input when word changes
    if (inputRef.current) {
      inputRef.current.focus();
    }

    // Auto-play audio when new word appears
    if (gameState.currentWord?.audio_url) {
      const timer = setTimeout(() => {
        playPronunciation(
          gameState.currentWord?.spanish || gameState.currentWord?.word || '',
          'es',
          gameState.currentWord || undefined
        );
      }, 500); // Small delay to let the UI settle
      return () => clearTimeout(timer);
    }
  }, [gameState.currentWordIndex, gameState.currentWord, playPronunciation]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && userAnswer.trim()) {
      onSubmit();
    }
  };

  const getStreakColor = () => {
    if (streak >= 10) return 'text-purple-400';
    if (streak >= 5) return 'text-green-400';
    if (streak >= 3) return 'text-blue-400';
    return 'text-yellow-400';
  };

  const getStreakBg = () => {
    if (streak >= 10) return 'bg-purple-500/20 border-purple-400/30';
    if (streak >= 5) return 'bg-green-500/20 border-green-400/30';
    if (streak >= 3) return 'bg-blue-500/20 border-blue-400/30';
    return 'bg-yellow-500/20 border-yellow-400/30';
  };

  // Enhanced styling for both adventure and mastery modes
  const adventureTheme = getAdventureTheme('recall');
  
  const containerClasses = isAdventureMode
    ? adventureTheme.background
    : "min-h-screen bg-gray-50 font-sans antialiased";

  const cardClasses = isAdventureMode
    ? adventureTheme.cardStyle
    : "bg-white rounded-2xl shadow-lg p-7 border border-gray-100";

  const inputCardClasses = isAdventureMode
    ? adventureTheme.cardStyle
    : "bg-white rounded-2xl shadow-lg p-6 border border-gray-100";

  const textPrimary = isAdventureMode ? "text-white" : "text-gray-900";
  const textSecondary = isAdventureMode ? "text-white/80" : "text-gray-600";
  const textMuted = isAdventureMode ? "text-white/60" : "text-gray-500";

  if (!isAdventureMode) {
    // Layout matching the screenshot
    return (
      <div className="min-h-screen bg-gray-50 flex">
        {/* Main content area */}
        <div className="flex-1 p-8 flex flex-col justify-center">
          <div className="max-w-2xl mx-auto w-full space-y-7">
            {/* Word display card */}
            <motion.div
              key={gameState.currentWordIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={cardClasses}
            >
              <div className="text-center space-y-6">
                <div className="text-4xl text-purple-500">
                  <Brain className="h-12 w-12 mx-auto" />
                </div>

                <h2 className="text-xl font-bold text-gray-800 flex items-center justify-center">
                  <Brain className="h-5 w-5 mr-2" />
                  Recall Challenge
                </h2>

                <h3 className="text-5xl font-extrabold text-purple-700">
                  {gameState.currentWord?.spanish || gameState.currentWord?.word}
                </h3>

                {/* Part of speech */}
                {gameState.currentWord?.part_of_speech && (
                  <div className="inline-block px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-700 font-medium">
                    {gameState.currentWord.part_of_speech}
                  </div>
                )}

                {/* Audio button */}
                {gameState.currentWord?.audio_url && (
                  <button
                    onClick={() => playPronunciation(gameState.currentWord?.spanish || '', 'es', gameState.currentWord || undefined)}
                    disabled={gameState.audioPlaying}
                    className="p-4 rounded-full bg-purple-500 hover:bg-purple-600 text-white shadow-md transition-all duration-200 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    <Volume2 className="h-7 w-7" />
                  </button>
                )}
              </div>
            </motion.div>

            {/* Input area */}
            <div className={inputCardClasses}>
              <div className="space-y-5">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-800">
                    Type the English translation:
                  </h3>

                  <button
                    className="flex items-center space-x-2 px-3 py-1 rounded-xl text-sm transition-colors border border-gray-300 text-gray-600 hover:bg-gray-100"
                  >
                    <Lightbulb className="h-4 w-4" />
                    <span>Hint</span>
                  </button>
                </div>

                <div className="relative">
                  <input
                    ref={inputRef}
                    type="text"
                    value={userAnswer}
                    onChange={(e) => onAnswerChange(e.target.value)}
                    onKeyDown={handleKeyPress}
                    placeholder="Type your answer..."
                    className="w-full p-4 rounded-xl text-lg font-medium bg-gray-50 text-gray-900 placeholder-gray-400 border border-gray-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-100 transition-all duration-200 outline-none"
                    autoFocus
                  />
                </div>

                <button
                  onClick={onSubmit}
                  disabled={!userAnswer.trim()}
                  className={`w-full py-3 rounded-xl font-semibold text-white transition-all duration-200 shadow-md ${
                    userAnswer.trim()
                      ? 'bg-purple-600 hover:bg-purple-700'
                      : 'bg-purple-300 cursor-not-allowed'
                  }`}
                >
                  Check Answer
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Right sidebar - same as LearnMode */}
        <div className="w-80 p-8 space-y-6 bg-gray-100 border-l border-gray-200">
          {/* Performance card */}
          <div className="bg-white rounded-2xl shadow-sm p-6 text-gray-800 border border-gray-100">
            <h4 className="text-xl font-bold mb-4">Performance</h4>

            <div className="space-y-4">
              <div className="bg-purple-50 rounded-xl p-4 flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Target className="h-5 w-5 text-purple-500" />
                  <span className="font-medium">Streak</span>
                </div>
                <span className="text-2xl font-bold text-purple-700">{streak}</span>
              </div>

              <div className="bg-purple-50 rounded-xl p-4 flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Target className="h-5 w-5 text-purple-500" />
                  <span className="font-medium">Accuracy</span>
                </div>
                <span className="text-2xl font-bold text-purple-700">
                  {gameState.totalWords > 0 ? Math.round((gameState.correctAnswers / (gameState.correctAnswers + gameState.incorrectAnswers || 1)) * 100) : 0}%
                </span>
              </div>

              <div className="bg-purple-50 rounded-xl p-4 flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Star className="h-5 w-5 text-purple-500" />
                  <span className="font-medium">0 XP</span>
                </div>
                <div className="text-right">
                  <div className="text-xs text-gray-500">Level 1</div>
                </div>
              </div>
            </div>
          </div>

          {/* Session Progress card */}
          <div className="bg-white rounded-2xl shadow-sm p-6 text-gray-800 border border-gray-100">
            <h4 className="text-xl font-bold mb-4">Session Progress</h4>

            <div className="space-y-3">
              <div className="bg-purple-100 rounded-full h-3 overflow-hidden">
                <div
                  className="bg-gradient-to-r from-purple-400 to-purple-600 h-full rounded-full transition-all duration-500"
                  style={{ width: `${((gameState.currentWordIndex + 1) / gameState.totalWords) * 100}%` }}
                ></div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold text-purple-700">
                  {Math.round(((gameState.currentWordIndex + 1) / gameState.totalWords) * 100)}% Complete
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  {gameState.totalWords - (gameState.currentWordIndex + 1)} words remaining
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Adventure mode layout (keep existing)
  return (
    <div className={`${containerClasses} p-6`}>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Adventure Mode Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-6"
        >
          <h1 className="text-4xl font-bold text-white mb-2">
            {adventureTheme.emoji} {adventureTheme.name}
          </h1>
          <p className="text-white/80 text-lg">
            {adventureTheme.description}
          </p>
        </motion.div>

        {/* Word display with streak */}
        <motion.div
          key={gameState.currentWordIndex}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={cardClasses}
        >
          <div className="text-center space-y-6">
            <div className="text-6xl">
              <Brain className={`h-16 w-16 mx-auto ${isAdventureMode ? adventureTheme.accentColor === 'purple' ? 'text-purple-300' : 'text-violet-300' : 'text-purple-300'}`} />
            </div>
            
            <h2 className="text-2xl font-bold text-white flex items-center justify-center">
              <Brain className="h-6 w-6 mr-2" />
              Mastery Challenge
            </h2>

            {/* Streak display */}
            {streak > 0 && (
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full border ${getStreakBg()}`}
              >
                <Target className={`h-4 w-4 ${getStreakColor()}`} />
                <span className={`font-bold ${getStreakColor()}`}>
                  {streak} streak!
                </span>
              </motion.div>
            )}

            <div className="space-y-4">
              <h3 className="text-5xl font-bold text-white">
                {gameState.currentWord?.spanish || gameState.currentWord?.word}
              </h3>

              {/* Part of speech */}
              {gameState.currentWord?.part_of_speech && (
                <div className="inline-block px-3 py-1 rounded-full text-sm bg-purple-500/20 text-purple-200 border border-purple-400/30">
                  {gameState.currentWord.part_of_speech}
                </div>
              )}

              {/* Audio button */}
              {gameState.currentWord?.audio_url && (
                <button
                  onClick={() => playPronunciation(gameState.currentWord?.spanish || '', 'es', gameState.currentWord || undefined)}
                  disabled={gameState.audioPlaying}
                  className={`p-4 rounded-full transition-colors border-2 shadow-lg ${
                    gameState.audioPlaying
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed border-gray-300'
                      : 'bg-blue-500/30 hover:bg-blue-500/40 text-blue-200 border-blue-400/50'
                  }`}
                >
                  <Volume2 className="h-6 w-6" />
                </button>
              )}
            </div>

            <div className="text-sm text-white/60">
              Test your memory - no hints this time!
            </div>
          </div>
        </motion.div>

        {/* Input area */}
        <div className={cardClasses}>
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white">
              What does this word mean?
            </h3>
            
            <div className="relative">
              <input
                ref={inputRef}
                type="text"
                value={userAnswer}
                onChange={(e) => onAnswerChange(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder={getPlaceholderText('recall')}
                className="w-full p-4 rounded-xl text-lg font-medium transition-all duration-200 bg-slate-700/50 text-white placeholder-slate-400 border border-slate-500/30 focus:border-purple-400/50 focus:ring-2 focus:ring-purple-400/20"
                autoFocus
              />
            </div>

            {/* Action buttons */}
            <div className="flex space-x-3">
              <button
                onClick={onSubmit}
                disabled={!userAnswer.trim()}
                className={`flex-1 py-3 rounded-xl font-semibold transition-all duration-200 ${
                  userAnswer.trim()
                    ? 'bg-purple-500 hover:bg-purple-600 text-white shadow-lg hover:shadow-purple-500/25'
                    : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                }`}
              >
                Check Answer
              </button>

              {/* Hint button */}
              <button
                className="px-4 py-3 rounded-xl font-medium transition-all duration-200 border border-slate-600 text-slate-300 hover:bg-slate-700/50"
                title="Show hint"
              >
                <Lightbulb className="h-5 w-5" />
              </button>
            </div>

            {/* Progress indicators */}
            <div className="flex justify-center space-x-2">
              {Array.from({ length: Math.min(streak, 10) }, (_, i) => (
                <motion.div
                  key={i}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: i * 0.1 }}
                  className={`w-2 h-2 rounded-full ${getStreakColor().replace('text-', 'bg-')}`}
                />
              ))}
            </div>

            {streak >= 10 && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center p-3 rounded-xl bg-purple-500/20 text-purple-200 border border-purple-400/30"
              >
                <p className="text-sm font-semibold">
                  🔥 Amazing! You're on fire with a {streak} word streak!
                </p>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
