WHen I load the page, this happens.

audioFeedbackService.ts:36 🎵 AudioFeedbackService: Preloading audio files...
audioFeedbackService.ts:44 💎 Preloaded gem sound: common -> /audio/gems/gem-common.mp3
audioFeedbackService.ts:44 💎 Preloaded gem sound: uncommon -> /audio/gems/gem-uncommon.mp3
audioFeedbackService.ts:44 💎 Preloaded gem sound: rare -> /audio/gems/gem-rare.mp3
audioFeedbackService.ts:44 💎 Preloaded gem sound: epic -> /audio/gems/gem-epic.mp3
audioFeedbackService.ts:44 💎 Preloaded gem sound: legendary -> /audio/gems/gem-legendary.mp3
audioFeedbackService.ts:53 🏆 Preloaded achievement sound: unlock -> /audio/achievements/achievement-unlock.mp3
audioFeedbackService.ts:53 🏆 Preloaded achievement sound: rare -> /audio/achievements/achievement-rare.mp3
audioFeedbackService.ts:53 🏆 Preloaded achievement sound: legendary -> /audio/achievements/achievement-legendary.mp3
audioFeedbackService.ts:62 🔊 Preloaded feedback sound: correct -> /audio/sfx/correct-answer.mp3
audioFeedbackService.ts:62 🔊 Preloaded feedback sound: incorrect -> /audio/sfx/wrong-answer.mp3
audioFeedbackService.ts:62 🔊 Preloaded feedback sound: levelComplete -> /audio/battle/victory.mp3
audioFeedbackService.ts:65 ✅ AudioFeedbackService: Audio preloading complete!
audioFeedbackService.ts:66 📋 Cached audio keys: (11) ['gem-common', 'gem-uncommon', 'gem-rare', 'gem-epic', 'gem-legendary', 'achievement-unlock', 'achievement-rare', 'achievement-legendary', 'feedback-correct', 'feedback-incorrect', 'feedback-levelComplete']
 Cart items changed: []
 Loading cart from localStorage: []
 Starting auth initialization...
 Fetching current session...
 Cart items changed: []
 Loading cart from localStorage: []
 Starting auth initialization...
 Fetching current session...
 ClientLayout - pathname: /vocabmaster
 ClientLayout - isClient: false
 ClientLayout - isOnStudentSubdomain: false
 ClientLayout - pathname: /vocabmaster
 ClientLayout - isClient: false
 ClientLayout - isOnStudentSubdomain: false
 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
 🎨 UnifiedVocabMasterLauncher RENDER
 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 0}
 🎯 Rendering categories dropdown: {availableCategories: Array(0), count: 0}
 🎨 UnifiedVocabMasterLauncher RENDER
 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 0}
 🎯 Rendering categories dropdown: {availableCategories: Array(0), count: 0}
 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
 🚨 CATEGORIES USEEFFECT RUNNING!
 🔄 useEffect for categories triggered: {selectedLanguage: 'spanish', selectedLevel: 'KS3', hasSupabase: true, currentCategoriesCount: 0, supabaseType: 'object', …}
 🚀 About to load categories for: spanish
 🔄 Loading categories for language: es level: KS3
 🔄 useEffect for subtopics triggered: {selectedCategory: '', hasSupabase: true}
 🔍 Loading user stats for: {userId: undefined, hasSupabase: true, selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: ''}
 ❌ No user or supabase, cannot load real stats
 🔄 URL params useEffect running. Current gameState: launcher
 🔄 isAssignmentMode: false
 🔄 searchParams: {lang: undefined, level: undefined, cat: undefined, subcat: undefined, theme: undefined, …}
 🔍 VocabMaster checking URL params: {lang: undefined, level: undefined, cat: undefined, subcat: undefined}
 ❌ No URL parameters - will show launcher with filter selectors
 🚀 Setting gameState to launcher (no params)
 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: false, hasUnifiedUser: false, userId: undefined, isDemo: false}
 🎮 Demo mode detected - skipping service initialization
 ClientLayout - hostname: localhost
 ClientLayout - isStudentDomain: false
 Cart items changed: []
 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:99 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
UnifiedVocabMasterLauncher.tsx:468 🚨 CATEGORIES USEEFFECT RUNNING!
UnifiedVocabMasterLauncher.tsx:469 🔄 useEffect for categories triggered: {selectedLanguage: 'spanish', selectedLevel: 'KS3', hasSupabase: true, currentCategoriesCount: 0, supabaseType: 'object', …}
UnifiedVocabMasterLauncher.tsx:478 🚀 About to load categories for: spanish
UnifiedVocabMasterLauncher.tsx:399 🔄 Loading categories for language: es level: KS3
UnifiedVocabMasterLauncher.tsx:491 🔄 useEffect for subtopics triggered: {selectedCategory: '', hasSupabase: true}
UnifiedVocabMasterLauncher.tsx:525 🔍 Loading user stats for: {userId: undefined, hasSupabase: true, selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: ''}
UnifiedVocabMasterLauncher.tsx:534 ❌ No user or supabase, cannot load real stats
UnifiedVocabMasterWrapper.tsx:59 🔄 URL params useEffect running. Current gameState: launcher
UnifiedVocabMasterWrapper.tsx:60 🔄 isAssignmentMode: false
UnifiedVocabMasterWrapper.tsx:61 🔄 searchParams: {lang: undefined, level: undefined, cat: undefined, subcat: undefined, theme: undefined, …}
UnifiedVocabMasterWrapper.tsx:69 🔍 VocabMaster checking URL params: {lang: undefined, level: undefined, cat: undefined, subcat: undefined}
UnifiedVocabMasterWrapper.tsx:97 ❌ No URL parameters - will show launcher with filter selectors
UnifiedVocabMasterWrapper.tsx:98 🚀 Setting gameState to launcher (no params)
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: false, hasUnifiedUser: false, userId: undefined, isDemo: false}
UnifiedVocabMasterWrapper.tsx:128 🎮 Demo mode detected - skipping service initialization
ClientLayout.tsx:20 ClientLayout - hostname: localhost
ClientLayout.tsx:21 ClientLayout - isStudentDomain: false
AuthProvider.tsx:273 Auth state change event: SIGNED_IN Session: true
vocabmaster:1 Denying load of chrome-extension://aggiiclaiamajehmlfpkjmlbadmkledi/popup.js. Resources must be listed in the web_accessible_resources manifest key in order to be loaded by pages outside the extension.
vocabmaster:1 Denying load of chrome-extension://aggiiclaiamajehmlfpkjmlbadmkledi/tat_popup.js. Resources must be listed in the web_accessible_resources manifest key in order to be loaded by pages outside the extension.
ClientLayout.tsx:29 ClientLayout - pathname: /vocabmaster
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
ClientLayout.tsx:29 ClientLayout - pathname: /vocabmaster
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(0), count: 0}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(0), count: 0}
  GET chrome-extension://invalid/ net::ERR_FAILED
g @ contentscript.js:31
(anonymous) @ contentscript.js:35
e @ contentscript.js:26
t @ contentscript.js:26
setTimeout
(anonymous) @ contentscript.js:26
i @ contentscript.js:26
fireWith @ contentscript.js:26
fire @ contentscript.js:26
i @ contentscript.js:26
fireWith @ contentscript.js:26
ready @ contentscript.js:26
setTimeout
(anonymous) @ contentscript.js:26
(anonymous) @ contentscript.js:15
755 @ contentscript.js:15
k @ contentscript.js:26
(anonymous) @ contentscript.js:26
(anonymous) @ contentscript.js:35
(anonymous) @ contentscript.js:35
 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
 🚨 CATEGORIES USEEFFECT RUNNING!
 🔄 useEffect for categories triggered: {selectedLanguage: 'spanish', selectedLevel: 'KS3', hasSupabase: true, currentCategoriesCount: 0, supabaseType: 'object', …}
 🚀 About to load categories for: spanish
 🔄 Loading categories for language: es level: KS3
 🔄 useEffect for subtopics triggered: {selectedCategory: '', hasSupabase: true}
 🔍 Loading user stats for: {userId: undefined, hasSupabase: true, selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: ''}
 ❌ No user or supabase, cannot load real stats
 🔄 URL params useEffect running. Current gameState: launcher
 🔄 isAssignmentMode: false
 🔄 searchParams: {lang: undefined, level: undefined, cat: undefined, subcat: undefined, theme: undefined, …}
 🔍 VocabMaster checking URL params: {lang: undefined, level: undefined, cat: undefined, subcat: undefined}
 ❌ No URL parameters - will show launcher with filter selectors
 🚀 Setting gameState to launcher (no params)
 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: false, hasUnifiedUser: false, userId: undefined, isDemo: false}
 🎮 Demo mode detected - skipping service initialization
 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
 🚨 CATEGORIES USEEFFECT RUNNING!
 🔄 useEffect for categories triggered: {selectedLanguage: 'spanish', selectedLevel: 'KS3', hasSupabase: true, currentCategoriesCount: 0, supabaseType: 'object', …}
 🚀 About to load categories for: spanish
 🔄 Loading categories for language: es level: KS3
 🔄 useEffect for subtopics triggered: {selectedCategory: '', hasSupabase: true}
 🔍 Loading user stats for: {userId: undefined, hasSupabase: true, selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: ''}
 ❌ No user or supabase, cannot load real stats
 🔄 URL params useEffect running. Current gameState: launcher
 🔄 isAssignmentMode: false
 🔄 searchParams: {lang: undefined, level: undefined, cat: undefined, subcat: undefined, theme: undefined, …}
 🔍 VocabMaster checking URL params: {lang: undefined, level: undefined, cat: undefined, subcat: undefined}
 ❌ No URL parameters - will show launcher with filter selectors
 🚀 Setting gameState to launcher (no params)
 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: false, hasUnifiedUser: false, userId: undefined, isDemo: false}
 🎮 Demo mode detected - skipping service initialization
 🎨 UnifiedVocabMasterLauncher RENDER
 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
 🎨 UnifiedVocabMasterLauncher RENDER
 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
 [Vercel Web Analytics] Debug mode is enabled by default in development. No requests will be sent to the server.
 [Vercel Web Analytics] Running queued event pageview {route: '/vocabmaster', path: '/vocabmaster'}
 [Vercel Web Analytics] Running queued event pageview {route: '/vocabmaster', path: '/vocabmaster'}
 [Vercel Web Analytics] [pageview] http://localhost:3001/vocabmaster {o: 'http://localhost:3001/vocabmaster', sv: '0.1.3', sdkn: '@vercel/analytics/next', sdkv: '1.5.0', ts: 1754512743184, …}
 [Vercel Web Analytics] [pageview] http://localhost:3001/vocabmaster {o: 'http://localhost:3001/vocabmaster', sv: '0.1.3', sdkn: '@vercel/analytics/next', sdkv: '1.5.0', ts: 1754512743184, …}
 MainNavigation component loaded!
 MainNavigation component loaded!
 Session fetched, updating auth state... true
 Session fetched, updating auth state... true
 updateAuthState called with session: true
 Getting user data for: <EMAIL>
 Setting admin role for email: <EMAIL>
 getUserData returning for admin: {role: 'admin', hasSubscription: true}
 Setting user role in state: admin
 Setting subscription status: true
 updateAuthState completed
 Auth initialization completed successfully
 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
 🎨 UnifiedVocabMasterLauncher RENDER
 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
 🎨 UnifiedVocabMasterLauncher RENDER
 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: true, hasUnifiedUser: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', isDemo: false}
 ✅ Initializing VocabMaster services for user: 9efcdbe9-7116-4bb7-a696-4afb0fb34e4c
 Starting cart sync with server for user: 9efcdbe9-7116-4bb7-a696-4afb0fb34e4c
 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
 🎨 UnifiedVocabMasterLauncher RENDER
 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
AuthProvider.tsx:273 Auth state change event: INITIAL_SESSION Session: true
AuthProvider.tsx:150 updateAuthState called with session: true
AuthProvider.tsx:156 Getting user data for: <EMAIL>
AuthProvider.tsx:102 Setting admin role for email: <EMAIL>
AuthProvider.tsx:104 getUserData returning for admin: {role: 'admin', hasSubscription: true}
AuthProvider.tsx:158 Setting user role in state: admin
AuthProvider.tsx:159 Setting subscription status: true
AuthProvider.tsx:171 updateAuthState completed
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: true, hasUnifiedUser: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', isDemo: false}
UnifiedVocabMasterWrapper.tsx:136 ✅ Initializing VocabMaster services for user: 9efcdbe9-7116-4bb7-a696-4afb0fb34e4c
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
UnifiedVocabMasterLauncher.tsx:419 ✅ Setting categories in state: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
UnifiedVocabMasterLauncher.tsx:421 🔄 Loaded categories for language: es categories: 11
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
UnifiedVocabMasterLauncher.tsx:419 ✅ Setting categories in state: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
UnifiedVocabMasterLauncher.tsx:421 🔄 Loaded categories for language: es categories: 11
CartContext.tsx:226 Server cart data: []
CartContext.tsx:227 Current local cart: []
CartContext.tsx:238 Both local and server carts are empty, skipping sync
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
UnifiedVocabMasterLauncher.tsx:419 ✅ Setting categories in state: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
UnifiedVocabMasterLauncher.tsx:421 🔄 Loaded categories for language: es categories: 11
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
UnifiedVocabMasterLauncher.tsx:419 ✅ Setting categories in state: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
UnifiedVocabMasterLauncher.tsx:421 🔄 Loaded categories for language: es categories: 11
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:67 Auth state changed in MainNavigation: {isAuthenticated: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', email: '<EMAIL>', role: 'admin'}
MainNavigation.tsx:67 Auth state changed in MainNavigation: {isAuthenticated: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', email: '<EMAIL>', role: 'admin'}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}


When I change to French, this happens. Full file.

audioFeedbackService.ts:36 🎵 AudioFeedbackService: Preloading audio files...
audioFeedbackService.ts:44 💎 Preloaded gem sound: common -> /audio/gems/gem-common.mp3
audioFeedbackService.ts:44 💎 Preloaded gem sound: uncommon -> /audio/gems/gem-uncommon.mp3
audioFeedbackService.ts:44 💎 Preloaded gem sound: rare -> /audio/gems/gem-rare.mp3
audioFeedbackService.ts:44 💎 Preloaded gem sound: epic -> /audio/gems/gem-epic.mp3
audioFeedbackService.ts:44 💎 Preloaded gem sound: legendary -> /audio/gems/gem-legendary.mp3
audioFeedbackService.ts:53 🏆 Preloaded achievement sound: unlock -> /audio/achievements/achievement-unlock.mp3
audioFeedbackService.ts:53 🏆 Preloaded achievement sound: rare -> /audio/achievements/achievement-rare.mp3
audioFeedbackService.ts:53 🏆 Preloaded achievement sound: legendary -> /audio/achievements/achievement-legendary.mp3
audioFeedbackService.ts:62 🔊 Preloaded feedback sound: correct -> /audio/sfx/correct-answer.mp3
audioFeedbackService.ts:62 🔊 Preloaded feedback sound: incorrect -> /audio/sfx/wrong-answer.mp3
audioFeedbackService.ts:62 🔊 Preloaded feedback sound: levelComplete -> /audio/battle/victory.mp3
audioFeedbackService.ts:65 ✅ AudioFeedbackService: Audio preloading complete!
audioFeedbackService.ts:66 📋 Cached audio keys: (11) ['gem-common', 'gem-uncommon', 'gem-rare', 'gem-epic', 'gem-legendary', 'achievement-unlock', 'achievement-rare', 'achievement-legendary', 'feedback-correct', 'feedback-incorrect', 'feedback-levelComplete']
CartContext.tsx:115 Cart items changed: []
CartContext.tsx:130 Loading cart from localStorage: []
AuthProvider.tsx:211 Starting auth initialization...
AuthProvider.tsx:227 Fetching current session...
CartContext.tsx:115 Cart items changed: []
CartContext.tsx:130 Loading cart from localStorage: []
AuthProvider.tsx:211 Starting auth initialization...
AuthProvider.tsx:227 Fetching current session...
ClientLayout.tsx:29 ClientLayout - pathname: /vocabmaster
ClientLayout.tsx:30 ClientLayout - isClient: false
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
ClientLayout.tsx:29 ClientLayout - pathname: /vocabmaster
ClientLayout.tsx:30 ClientLayout - isClient: false
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(0), count: 0}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(0), count: 0}
useVocabulary.ts:58 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:99 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
UnifiedVocabMasterLauncher.tsx:468 🚨 CATEGORIES USEEFFECT RUNNING!
UnifiedVocabMasterLauncher.tsx:469 🔄 useEffect for categories triggered: {selectedLanguage: 'spanish', selectedLevel: 'KS3', hasSupabase: true, currentCategoriesCount: 0, supabaseType: 'object', …}
UnifiedVocabMasterLauncher.tsx:478 🚀 About to load categories for: spanish
UnifiedVocabMasterLauncher.tsx:399 🔄 Loading categories for language: es level: KS3
UnifiedVocabMasterLauncher.tsx:491 🔄 useEffect for subtopics triggered: {selectedCategory: '', hasSupabase: true}
UnifiedVocabMasterLauncher.tsx:525 🔍 Loading user stats for: {userId: undefined, hasSupabase: true, selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: ''}
UnifiedVocabMasterLauncher.tsx:534 ❌ No user or supabase, cannot load real stats
UnifiedVocabMasterWrapper.tsx:59 🔄 URL params useEffect running. Current gameState: launcher
UnifiedVocabMasterWrapper.tsx:60 🔄 isAssignmentMode: false
UnifiedVocabMasterWrapper.tsx:61 🔄 searchParams: {lang: undefined, level: undefined, cat: undefined, subcat: undefined, theme: undefined, …}
UnifiedVocabMasterWrapper.tsx:69 🔍 VocabMaster checking URL params: {lang: undefined, level: undefined, cat: undefined, subcat: undefined}
UnifiedVocabMasterWrapper.tsx:97 ❌ No URL parameters - will show launcher with filter selectors
UnifiedVocabMasterWrapper.tsx:98 🚀 Setting gameState to launcher (no params)
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: false, hasUnifiedUser: false, userId: undefined, isDemo: false}
UnifiedVocabMasterWrapper.tsx:128 🎮 Demo mode detected - skipping service initialization
ClientLayout.tsx:20 ClientLayout - hostname: localhost
ClientLayout.tsx:21 ClientLayout - isStudentDomain: false
CartContext.tsx:115 Cart items changed: []
useVocabulary.ts:58 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:99 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
UnifiedVocabMasterLauncher.tsx:468 🚨 CATEGORIES USEEFFECT RUNNING!
UnifiedVocabMasterLauncher.tsx:469 🔄 useEffect for categories triggered: {selectedLanguage: 'spanish', selectedLevel: 'KS3', hasSupabase: true, currentCategoriesCount: 0, supabaseType: 'object', …}
UnifiedVocabMasterLauncher.tsx:478 🚀 About to load categories for: spanish
UnifiedVocabMasterLauncher.tsx:399 🔄 Loading categories for language: es level: KS3
UnifiedVocabMasterLauncher.tsx:491 🔄 useEffect for subtopics triggered: {selectedCategory: '', hasSupabase: true}
UnifiedVocabMasterLauncher.tsx:525 🔍 Loading user stats for: {userId: undefined, hasSupabase: true, selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: ''}
UnifiedVocabMasterLauncher.tsx:534 ❌ No user or supabase, cannot load real stats
UnifiedVocabMasterWrapper.tsx:59 🔄 URL params useEffect running. Current gameState: launcher
UnifiedVocabMasterWrapper.tsx:60 🔄 isAssignmentMode: false
UnifiedVocabMasterWrapper.tsx:61 🔄 searchParams: {lang: undefined, level: undefined, cat: undefined, subcat: undefined, theme: undefined, …}
UnifiedVocabMasterWrapper.tsx:69 🔍 VocabMaster checking URL params: {lang: undefined, level: undefined, cat: undefined, subcat: undefined}
UnifiedVocabMasterWrapper.tsx:97 ❌ No URL parameters - will show launcher with filter selectors
UnifiedVocabMasterWrapper.tsx:98 🚀 Setting gameState to launcher (no params)
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: false, hasUnifiedUser: false, userId: undefined, isDemo: false}
UnifiedVocabMasterWrapper.tsx:128 🎮 Demo mode detected - skipping service initialization
ClientLayout.tsx:20 ClientLayout - hostname: localhost
ClientLayout.tsx:21 ClientLayout - isStudentDomain: false
AuthProvider.tsx:273 Auth state change event: SIGNED_IN Session: true
vocabmaster:1 Denying load of chrome-extension://aggiiclaiamajehmlfpkjmlbadmkledi/popup.js. Resources must be listed in the web_accessible_resources manifest key in order to be loaded by pages outside the extension.
vocabmaster:1 Denying load of chrome-extension://aggiiclaiamajehmlfpkjmlbadmkledi/tat_popup.js. Resources must be listed in the web_accessible_resources manifest key in order to be loaded by pages outside the extension.
ClientLayout.tsx:29 ClientLayout - pathname: /vocabmaster
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
ClientLayout.tsx:29 ClientLayout - pathname: /vocabmaster
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(0), count: 0}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(0), count: 0}
contentscript.js:31  GET chrome-extension://invalid/ net::ERR_FAILED
g @ contentscript.js:31
(anonymous) @ contentscript.js:35
e @ contentscript.js:26
t @ contentscript.js:26
setTimeout
(anonymous) @ contentscript.js:26
i @ contentscript.js:26
fireWith @ contentscript.js:26
fire @ contentscript.js:26
i @ contentscript.js:26
fireWith @ contentscript.js:26
ready @ contentscript.js:26
setTimeout
(anonymous) @ contentscript.js:26
(anonymous) @ contentscript.js:15
755 @ contentscript.js:15
k @ contentscript.js:26
(anonymous) @ contentscript.js:26
(anonymous) @ contentscript.js:35
(anonymous) @ contentscript.js:35
useVocabulary.ts:58 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:99 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
UnifiedVocabMasterLauncher.tsx:468 🚨 CATEGORIES USEEFFECT RUNNING!
UnifiedVocabMasterLauncher.tsx:469 🔄 useEffect for categories triggered: {selectedLanguage: 'spanish', selectedLevel: 'KS3', hasSupabase: true, currentCategoriesCount: 0, supabaseType: 'object', …}
UnifiedVocabMasterLauncher.tsx:478 🚀 About to load categories for: spanish
UnifiedVocabMasterLauncher.tsx:399 🔄 Loading categories for language: es level: KS3
UnifiedVocabMasterLauncher.tsx:491 🔄 useEffect for subtopics triggered: {selectedCategory: '', hasSupabase: true}
UnifiedVocabMasterLauncher.tsx:525 🔍 Loading user stats for: {userId: undefined, hasSupabase: true, selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: ''}
UnifiedVocabMasterLauncher.tsx:534 ❌ No user or supabase, cannot load real stats
UnifiedVocabMasterWrapper.tsx:59 🔄 URL params useEffect running. Current gameState: launcher
UnifiedVocabMasterWrapper.tsx:60 🔄 isAssignmentMode: false
UnifiedVocabMasterWrapper.tsx:61 🔄 searchParams: {lang: undefined, level: undefined, cat: undefined, subcat: undefined, theme: undefined, …}
UnifiedVocabMasterWrapper.tsx:69 🔍 VocabMaster checking URL params: {lang: undefined, level: undefined, cat: undefined, subcat: undefined}
UnifiedVocabMasterWrapper.tsx:97 ❌ No URL parameters - will show launcher with filter selectors
UnifiedVocabMasterWrapper.tsx:98 🚀 Setting gameState to launcher (no params)
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: false, hasUnifiedUser: false, userId: undefined, isDemo: false}
UnifiedVocabMasterWrapper.tsx:128 🎮 Demo mode detected - skipping service initialization
useVocabulary.ts:58 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:99 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
UnifiedVocabMasterLauncher.tsx:468 🚨 CATEGORIES USEEFFECT RUNNING!
UnifiedVocabMasterLauncher.tsx:469 🔄 useEffect for categories triggered: {selectedLanguage: 'spanish', selectedLevel: 'KS3', hasSupabase: true, currentCategoriesCount: 0, supabaseType: 'object', …}
UnifiedVocabMasterLauncher.tsx:478 🚀 About to load categories for: spanish
UnifiedVocabMasterLauncher.tsx:399 🔄 Loading categories for language: es level: KS3
UnifiedVocabMasterLauncher.tsx:491 🔄 useEffect for subtopics triggered: {selectedCategory: '', hasSupabase: true}
UnifiedVocabMasterLauncher.tsx:525 🔍 Loading user stats for: {userId: undefined, hasSupabase: true, selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: ''}
UnifiedVocabMasterLauncher.tsx:534 ❌ No user or supabase, cannot load real stats
UnifiedVocabMasterWrapper.tsx:59 🔄 URL params useEffect running. Current gameState: launcher
UnifiedVocabMasterWrapper.tsx:60 🔄 isAssignmentMode: false
UnifiedVocabMasterWrapper.tsx:61 🔄 searchParams: {lang: undefined, level: undefined, cat: undefined, subcat: undefined, theme: undefined, …}
UnifiedVocabMasterWrapper.tsx:69 🔍 VocabMaster checking URL params: {lang: undefined, level: undefined, cat: undefined, subcat: undefined}
UnifiedVocabMasterWrapper.tsx:97 ❌ No URL parameters - will show launcher with filter selectors
UnifiedVocabMasterWrapper.tsx:98 🚀 Setting gameState to launcher (no params)
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: false, hasUnifiedUser: false, userId: undefined, isDemo: false}
UnifiedVocabMasterWrapper.tsx:128 🎮 Demo mode detected - skipping service initialization
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
script.debug.js:1 [Vercel Web Analytics] Debug mode is enabled by default in development. No requests will be sent to the server.
script.debug.js:1 [Vercel Web Analytics] Running queued event pageview {route: '/vocabmaster', path: '/vocabmaster'}
script.debug.js:1 [Vercel Web Analytics] Running queued event pageview {route: '/vocabmaster', path: '/vocabmaster'}
script.debug.js:1 [Vercel Web Analytics] [pageview] http://localhost:3001/vocabmaster {o: 'http://localhost:3001/vocabmaster', sv: '0.1.3', sdkn: '@vercel/analytics/next', sdkv: '1.5.0', ts: 1754512743184, …}
script.debug.js:1 [Vercel Web Analytics] [pageview] http://localhost:3001/vocabmaster {o: 'http://localhost:3001/vocabmaster', sv: '0.1.3', sdkn: '@vercel/analytics/next', sdkv: '1.5.0', ts: 1754512743184, …}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
AuthProvider.tsx:242 Session fetched, updating auth state... true
AuthProvider.tsx:242 Session fetched, updating auth state... true
AuthProvider.tsx:150 updateAuthState called with session: true
AuthProvider.tsx:156 Getting user data for: <EMAIL>
AuthProvider.tsx:102 Setting admin role for email: <EMAIL>
AuthProvider.tsx:104 getUserData returning for admin: {role: 'admin', hasSubscription: true}
AuthProvider.tsx:158 Setting user role in state: admin
AuthProvider.tsx:159 Setting subscription status: true
AuthProvider.tsx:171 updateAuthState completed
AuthProvider.tsx:252 Auth initialization completed successfully
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: true, hasUnifiedUser: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', isDemo: false}
UnifiedVocabMasterWrapper.tsx:136 ✅ Initializing VocabMaster services for user: 9efcdbe9-7116-4bb7-a696-4afb0fb34e4c
CartContext.tsx:208 Starting cart sync with server for user: 9efcdbe9-7116-4bb7-a696-4afb0fb34e4c
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
AuthProvider.tsx:273 Auth state change event: INITIAL_SESSION Session: true
AuthProvider.tsx:150 updateAuthState called with session: true
AuthProvider.tsx:156 Getting user data for: <EMAIL>
AuthProvider.tsx:102 Setting admin role for email: <EMAIL>
AuthProvider.tsx:104 getUserData returning for admin: {role: 'admin', hasSubscription: true}
AuthProvider.tsx:158 Setting user role in state: admin
AuthProvider.tsx:159 Setting subscription status: true
AuthProvider.tsx:171 updateAuthState completed
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: true, hasUnifiedUser: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', isDemo: false}
UnifiedVocabMasterWrapper.tsx:136 ✅ Initializing VocabMaster services for user: 9efcdbe9-7116-4bb7-a696-4afb0fb34e4c
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: true, availableCategoriesCount: 0}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
UnifiedVocabMasterLauncher.tsx:419 ✅ Setting categories in state: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
UnifiedVocabMasterLauncher.tsx:421 🔄 Loaded categories for language: es categories: 11
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
UnifiedVocabMasterLauncher.tsx:419 ✅ Setting categories in state: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
UnifiedVocabMasterLauncher.tsx:421 🔄 Loaded categories for language: es categories: 11
CartContext.tsx:226 Server cart data: []
CartContext.tsx:227 Current local cart: []
CartContext.tsx:238 Both local and server carts are empty, skipping sync
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
UnifiedVocabMasterLauncher.tsx:419 ✅ Setting categories in state: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
UnifiedVocabMasterLauncher.tsx:421 🔄 Loaded categories for language: es categories: 11
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
UnifiedVocabMasterLauncher.tsx:419 ✅ Setting categories in state: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
UnifiedVocabMasterLauncher.tsx:421 🔄 Loaded categories for language: es categories: 11
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:67 Auth state changed in MainNavigation: {isAuthenticated: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', email: '<EMAIL>', role: 'admin'}
MainNavigation.tsx:67 Auth state changed in MainNavigation: {isAuthenticated: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', email: '<EMAIL>', role: 'admin'}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'spanish', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'french', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'fr', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'french', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'fr', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:828 🎯 Category dropdown render: {selectedCategory: '', selectedCategoryType: 'string', selectedCategoryLength: 0, categoriesLoading: false, availableCategoriesCount: 11}
UnifiedVocabMasterLauncher.tsx:841 🎯 Rendering categories dropdown: {availableCategories: Array(11), count: 11}
useVocabulary.ts:58 useVocabularyByCategory: Starting fetch with params: {language: 'fr', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:99 🔍 About to execute query with filters: {language: 'fr', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
UnifiedVocabMasterLauncher.tsx:468 🚨 CATEGORIES USEEFFECT RUNNING!
UnifiedVocabMasterLauncher.tsx:469 🔄 useEffect for categories triggered: {selectedLanguage: 'french', selectedLevel: 'KS3', hasSupabase: true, currentCategoriesCount: 11, supabaseType: 'object', …}
UnifiedVocabMasterLauncher.tsx:478 🚀 About to load categories for: french
UnifiedVocabMasterLauncher.tsx:399 🔄 Loading categories for language: fr level: KS3
UnifiedVocabMasterLauncher.tsx:525 🔍 Loading user stats for: {userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', hasSupabase: true, selectedLanguage: 'french', selectedLevel: 'KS3', selectedCategory: ''}
UnifiedVocabMasterLauncher.tsx:548 🔍 Querying performance data for admin user: {userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', language: 'fr', level: 'KS3', category: ''}
UnifiedVocabMasterWrapper.tsx:165 🔄 VocabMaster filter changed: {language: 'french', curriculumLevel: 'KS3', categoryId: '', subcategoryId: ''}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'french', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'fr', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'french', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'fr', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:575 ✅ Performance data loaded: {recordCount: 0, sampleRecord: undefined}
UnifiedVocabMasterLauncher.tsx:419 ✅ Setting categories in state: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
UnifiedVocabMasterLauncher.tsx:421 🔄 Loaded categories for language: fr categories: 11
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'french', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'fr', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'french', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'fr', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: fr category: undefined subcategory: undefined
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'french', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'fr', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'french', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'fr', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:590 ✅ Vocabulary progress data loaded: {recordCount: 80}
UnifiedVocabMasterLauncher.tsx:635 ✅ Final user stats calculated: {wordsLearned: 0, totalWords: 500, currentStreak: 0, weeklyGoal: 50, weeklyProgress: 0, …}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'french', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'fr', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:171 🎨 UnifiedVocabMasterLauncher RENDER
UnifiedVocabMasterLauncher.tsx:219 🚀 LAUNCHER STATE INITIALIZED: {selectedLanguage: 'french', selectedLevel: 'KS3', selectedCategory: '', selectedSubcategory: '', urlParams: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'fr', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
